import pandas as pd
import re
import os
import glob

def select_excel_file():
    """Allow user to select an Excel file from the current directory"""
    # Get current directory
    current_dir = os.getcwd()
    print(f"📁 Current directory: {current_dir}")

    # Find all Excel files in the current directory
    excel_files = []
    for extension in ['*.xlsx', '*.xls']:
        excel_files.extend(glob.glob(extension))

    if not excel_files:
        print("❌ No Excel files found in the current directory.")
        print("Please place your Excel file in the same folder as this script and try again.")
        return None

    print(f"\n📋 Found {len(excel_files)} Excel file(s):")
    for i, file in enumerate(excel_files, 1):
        file_size = os.path.getsize(file) / 1024  # Size in KB
        print(f"  {i}. {file} ({file_size:.1f} KB)")

    while True:
        try:
            choice = input(f"\n🔢 Select a file (1-{len(excel_files)}) or 'q' to quit: ").strip()

            if choice.lower() == 'q':
                print("👋 Goodbye!")
                return None

            choice_num = int(choice)
            if 1 <= choice_num <= len(excel_files):
                selected_file = excel_files[choice_num - 1]
                print(f"✅ Selected: {selected_file}")
                return selected_file
            else:
                print(f"❌ Please enter a number between 1 and {len(excel_files)}")

        except ValueError:
            print("❌ Please enter a valid number or 'q' to quit")
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            return None

# Improved extraction function
def extract_car_details(car_detail):
    if pd.isna(car_detail):
        return None, None, None, None

    car_detail = str(car_detail).upper()

    # Correct common typos and add Russian model names
    corrections = {
        # Brand corrections
        'HYNUDAI': 'HYUNDAI',
        'HUYNADA': 'HYUNDAI',
        'ХУНДАЙ': 'HYUNDAI',
        'HYUNDAY': 'HYUNDAI',
        'NYUNDAI': 'HYUNDAI',
        'HYUDAI': 'HYUNDAI',
        'HYUNDA': 'HYUNDAI',
        'HYNDAI': 'HYUNDAI',
        'HYUBDAI': 'HYUNDAI',
        'HUNDAI': 'HYUNDAI',
        'XYUNDAI': 'HYUNDAI',
        'ХЮНДАЙ': 'HYUNDAI',
        'ХНДАЙ': 'HYUNDAI',
        'HYUNDDAI': 'HYUNDAI',
        'ХЕНДЭ': 'HYUNDAI',
        'ТОЙОТА': 'TOYOTA',
        'ТОЙОТО': 'TOYOTA',
        'ТОYОТА': 'TOYOTA',
        'TAYOTA': 'TOYOTA',
        'TOYOITA': 'TOYOTA',
        'TOYTOA': 'TOYOTA',
        'ТАЙОТА': 'TOYOTA',
        'КИА': 'KIA',
        'КИЯ': 'KIA',
        'КIA': 'KIA',
        'KA': 'KIA',
        'КИA': 'KIA',
        'ОПЕЛЬ': 'OPEL',
        'ОПЕЛ': 'OPEL',
        'ЛЕКСУС': 'LEXUS',
        'ЛЕКСУC': 'LEXUS',
        'ШЕВРОЛЕТ': 'CHEVROLET',
        'ШЕВРАЛЕТ': 'CHEVROLET',
        'SHEVROLET': 'CHEVROLET',
        'МЕРСЕДЕС БЕНЗ': 'MERCEDES',
        'МЕРСЕДЕС BENZ': 'MERCEDES',
        'МЕРСЕДЕС БЕНЦ': 'MERCEDES',
        'МЕРС БЕНС': 'MERCEDES',
        'MERCEDE-BENZ': 'MERCEDES',
        'MERSEDES-BENZ': 'MERCEDES',
        'МЕРСЕДЕС БЕНС': 'MERCEDES',
        'MERCEDEC BENZ': 'MERCEDES',
        'М-БЕНС': 'MERCEDES',
        'MERCRDES-BENZ': 'MERCEDES',
        'МЕРСЕДЕС-БЕНЦ': 'MERCEDES',
        'МЕРСЕДЕС': 'MERCEDES',
        'МЕРСЕДЕС-БЕНЖ': 'MERCEDES',
        'BENZ': 'MERCEDES',
        'МЕРС БЕНЦ': 'MERCEDES',
        'M-BENZ': 'MERCEDES',
        'JETOURE': 'JETOUR',
        'BWW': 'BMW',
        'БМВ': 'BMW',
        'ФОЛЬКСВАГЕН': 'VOLKSWAGEN',
        'ТOYOTA': 'TOYOTA',
        'ЛАДА': 'LADA',
        'SSAN GYONG': 'SSANGYONG',
        'САНГ ЙОНГ': 'SSANGYONG',
        'SSNGYONG': 'SSANGYONG',
        'САНГЙОНГ': 'SSANGYONG',
        'SSANGYOUNG': 'SSANGYONG',
        'САНЬЕНГ': 'SSANGYONG',
        'НИССАН': 'NISSAN',
        'POLAR STONE': 'ROX',
        'CHEVORLET': 'CHEVROLET',
        'СHEVRОLET': 'CHEVROLET',
        'CHVROLET': 'CHEVROLET',
        'СHEVROLET': 'CHEVROLET',
        'CHEWROLET': 'CHEVROLET',
        'MITSUBSHI': 'MITSUBISHI',
        'ХОНДА': 'HONDA',
        'L9': 'LI',
        'L6': 'LI',
        'L7': 'LI',
        'ФОРД': 'FORD',
        'DAIHATSUI': 'DAIHATSU',
        'АУДИ': 'AUDI',
        'PORSHE': 'PORSCHE',
        'HUNTER': 'CHANGAN',
        'CS75': 'CHANGAN',
        'UNI-V': 'CHANGAN',
        'UNI-T': 'CHANGAN',
        'DOGE': 'DODGE',
        'LEXSUS': 'LEXUS',
        'LAXUS': 'LEXUS',
        'LEUS': 'LEXUS',
        'LRXUS': 'LEXUS',
        'JIETU': 'JETOUR',
        'JETOURE': 'JETOUR',
        'INFINITY': 'INFINITI',
        'РЕНО': 'RENAULT',
        'WOLKSWAGEN': 'VOLKSWAGEN',
        'JEELY': 'GEELY',
        'ФОЛКСВАГЕН': 'VOLKSWAGEN',
        'ВОЛГСВАГЕН': 'VOLKSWAGEN',
        'ШКОДА': 'SKODA',
        'РЕНДЖ РОВЕР': 'RANGE ROVER',
        'BASIC': 'BAIC',
        'GELLY': 'GEELY',
        'AIAN': 'AION',
        'BMV': 'BMW',
        'NETTA': 'NETA',
        'RENGE ROVER': 'RANGE ROVER',
        'ЛЕНД РОВЕР': 'LAND ROVER',
        'NUSSAN': 'NISSAN',
        'ТЕСЛА': 'TESLA',
        'ИНФИНИТИ': 'INFINITI',
        'TOPYOTA': 'TOYOTA',
        'KNA': 'KIA',
        'МАЗДА': 'MAZDA',
        'WEILMASTER': 'WELTMEISTER',
        'WIELMASTER': 'WELTMEISTER',
        'WEIMA': 'WELTMEISTER',
        'ФОЛЬСВАГЕН': 'VOLKSWAGEN',
        'HONQI': 'HONGQI',
        'ДОНГФЕНГ': 'DONGFENG',
        'АION': 'AION',
        'WILL MASTER': 'WELTMEISTER',
        'WALTMEISTER': 'WELTMEISTER',
        'HONG QI': 'HONGQI',
        'AYON': 'AION',
        'DONFENG': 'DONGFENG',
        'ГЕРКУЛЕС': 'HERCULES',
        'FANG CHENG BAO': 'BYD FANG CHENG BAO',
        # Fix brand confusion issues
        'AION S 580': 'AION S 580',  # Prevent AION from being confused with other brands
        'AION S MAX': 'AION S MAX',
        'AION Y': 'AION Y',
        'AION-Y': 'AION-Y',
        # Model name corrections (Russian to English)
        'САНТА ФЕ': 'SANTA FE',
        'САНТА-ФЕ': 'SANTA FE',
        'АВЕНТЕ': 'AVANTE',
        'АВАНТ': 'AVANTE',
        'ГРАНД СТАРЕКС': 'GRAND STAREX',
        'ГРАНД-СТАРЕКС': 'GRAND STAREX',
        'СПОРТЕДХ': 'SPORTAGE',
        'СПОРТАЖ': 'SPORTAGE',
        'СПОРТЕЙДЖ': 'SPORTAGE',
        'КОРОЛА': 'COROLLA',
        'КОРРОЛЛА': 'COROLLA',
        'ХАЙЛЕНДЕР': 'HIGHLANDER',
        'ХАЙЛАНДЕР': 'HIGHLANDER',
        'КАМРИ': 'CAMRY',
        'ПРАДО': 'PRADO',
        'ЛЕНД КРУЗЕР': 'LAND CRUISER',
        'ЛЭНД КРУЗЕР': 'LAND CRUISER'
    }
    for wrong, correct in corrections.items():
        car_detail = car_detail.replace(wrong, correct)

    # Extract year (ignore engine sizes like 2500 СМ3)
    # Try to extract 4-digit year first
    year_match = re.search(r'(?<!СМ)(?<!СМ3)[^\d]*((?:19|20)\d{2})(?!\d)', car_detail)

    if year_match:
        year = year_match.group(1)
    else:
        # Fallback: try to extract short year like '23' from formats like '06.12.23Г.'
        short_year_match = re.search(r'(\d{2})[ГГ]\.?', car_detail)
        if short_year_match:
            short_year = int(short_year_match.group(1))
            # Convert to full year assuming 2000s (adjust if your data has older cars)
            year = f"20{short_year:02d}" if short_year < 50 else f"19{short_year:02d}"
        else:
            year = None

    # Known car brands
    brands = ['TOYOTA', 'HYUNDAI', 'KIA', 'LEXUS', 'NISSAN', 'MITSUBISHI', 
          'HONDA', 'BMW', 'MERCEDES', 'AUDI', 'CHEVROLET', 'ZOTYE', 'ZERO',
          'ZEEKR', 'XPENG', 'XIAOMI', 'WULING', 'WELTMEISTER', 'VOYAH', 'VOLKSWAGEN',
          'VENUCIA', 'UAZ', 'TRUMPCHI', 'TANK', 'SSANG YONG', 'SKODA', 'SGMW', 'SELIS',
          'SEHOL', 'ROX', 'ROLLS ROYCE', 'ROEWE', 'RISING AUTO', 'RANGE ROVER', 'PORSCHE',
          'PENTIUM', 'OPEL', 'NIO', 'NETA', 'MG MOTOR', 'LI AUTO', 'LEAPMOTOR', 'LAND ROVER',
          'LADA', 'KAIWO', 'JIANGLING GROUP','JIANGLING','JETOUR','JAC', 'IM MOTORS', 'ICAR', 'HYCAN',
          'HUAWEI', 'HOTAI', 'HONGQI', 'HENGCHI', 'HE CHUANG', 'HAVAL', 'GWM', 'GEOMETRY',
          'GEOME', 'GEELY', 'FOTON', 'FEIFAN', 'FAW', 'EXEED', 'ENOVATE', 'DORCEN', 'DONGFENG',
          'DODGE', 'DENZA', 'CHERY', 'CHANGAN', 'BYD', 'BUICK', 'BAOLONG', 'BAOJUN', 'BAIC',
          'AVATR', 'ARCFOX', 'AITO', 'AION', 'LIXIANG', 'MERSEDES BENZ', 'GENESIS', 'FORD','CHEVROLET',
          'VOLVO', 'LI', 'RENAULT','JAECOO','INFINITI', 'SSANGYONG','CADILLAC','JEEP','LANDWIND', 'SUBARU',
          'SUZUKI','DAIHATSU','LINCOLN','DATSUN','LIAUTO','BEIJING','CHANGHE','RAM','MAZDA','GAC','DAEWOO',
          'GMC','JAGUAR','HAIMA','AIWAYS','CROZZ','SINOGOLD','TESLA','HOZON','WELTMEISTER',
          'ZHIJI','SKYWORTH','NEZHA','YOUNGER','HIPHI','FANG CHENG BAO','WEMAR',
          'RICH','SKYWELL','LEAP MOTOR','HERCULES','LEOPAARD','DOMY','HENREY','YEMA AUTO']

    # Enhanced brand and model extraction with conflict resolution
    brand = None
    model = None

    # Sort brands by length (longest first) to avoid partial matches
    sorted_brands = sorted(brands, key=len, reverse=True)

    # Special handling for brand conflicts and electric vehicle patterns
    brand_model_patterns = [
        # Electric vehicle specific patterns
        (r'ЭЛЕКТРОМОБИЛЬ\s+([A-Z]+)\s+([A-ZА-ЯЁ0-9\s\-]+?)(?:\s+Г[\.:]?ВЫП|\s+МОДЕЛЬ|\s+ГОД)', 'EV_PATTERN'),
        # Brand after "МАРКИ" keyword with model in middle of text
        (r'МАРКИ\s+([A-Z]+)\s+([A-ZА-ЯЁ0-9\s\-]+?)(?:\s+(?:ГОД|ГОДА|ВЫПУСКА|\d{4}))', 'MARKI_PATTERN'),
        # Brand and model in middle of complex text
        (r'МАРКИ\s+([A-Z]+)\s+([A-ZА-ЯЁ0-9\s\-]+?)\s+(?:ГОД|ГОДА|\d{4})', 'MIDDLE_PATTERN'),
        # Direct brand-model pattern
        (r'([A-Z]+)\s+([A-ZА-ЯЁ0-9\s\-]+?)(?:\s+(?:ГОД|ГОДА|ВЫПУСКА|МОДЕЛЬ|\d{4}|Г\.ВЫП))', 'DIRECT_PATTERN'),
        # Model first, then brand (for cases like "SANTA FE" without explicit brand)
        (r'МАРКИ\s+([A-ZА-ЯЁ0-9\s\-]+?)\s+(\d{4})', 'MODEL_FIRST_PATTERN'),
    ]

    # Try special patterns first
    for pattern, pattern_type in brand_model_patterns:
        match = re.search(pattern, car_detail)
        if match:
            potential_brand = match.group(1).strip()
            potential_model = match.group(2).strip() if len(match.groups()) > 1 else None

            # Validate brand against known brands
            if potential_brand in sorted_brands:
                brand = potential_brand
                model = potential_model
                break

    # Special case: try to infer brand from model if brand not found
    if not brand:
        # Check for known model-brand associations
        model_brand_map = {
            'SANTA FE': 'HYUNDAI', 'HIGHLANDER': 'TOYOTA', 'RAV 4': 'TOYOTA',
            'SPORTAGE': 'KIA', 'COROLLA': 'TOYOTA', 'CAMRY': 'TOYOTA',
            'AVANTE': 'HYUNDAI', 'ELANTRA': 'HYUNDAI', 'TUCSON': 'HYUNDAI'
        }

        for model_name, brand_name in model_brand_map.items():
            if model_name in car_detail and brand_name in sorted_brands:
                brand = brand_name
                model = model_name
                break

    # Fallback to original method if special patterns didn't work
    if not brand:
        for b in sorted_brands:
            if b in car_detail:
                brand = b
                rest = car_detail.split(b, 1)[1].strip()

                # Try to find model after "МОДЕЛЬ-" or "MODEL-" first (handle Cyrillic typos and various formats)
                model_patterns_explicit = [
                    # Ultra-specific patterns for exact failure cases
                    r'А/М\s+ЭЛЕКТРОМОБИЛЬ\s+МАРКА\s+AION\s+(LX\s+PLUS)\s+М[ОO]ДЕЛЬ',  # А/М ЭЛЕКТРОМОБИЛЬ МАРКА AION LX PLUS
                    r'А/М\s+ЭЛЕКТРОМОБИЛЬ\s+МАРКА\s+BYD\s+(YUAN\s+PLUS)\s+М[ОO]ДЕЛЬ', # А/М ЭЛЕКТРОМОБИЛЬ МАРКА BYD YUAN PLUS
                    r'А/М\s+ЭЛЕКТРОМОБИЛЬ\s+МАРКА\s+AION\s+(S\s+MAX)\s+М[ОO]ДЕЛЬ',    # А/М ЭЛЕКТРОМОБИЛЬ МАРКА AION S MAX
                    r'А/М\s+ЭЛЕКТРОМОБИЛЬ\s+XPENG\s+М[ОO]ДЕЛЬ:([A-Z0-9]+)',           # А/М ЭЛЕКТРОМОБИЛЬ XPENG МОДЕЛЬ:CODE
                    r'АВТОМОБИЛЬ:LAND\s+ROVER\(RENGE\s+(ROVER\s+SPORT)\)',             # LAND ROVER(RENGE ROVER SPORT)
                    # Standard patterns
                    r'МАРКА\s+AION\s+(LX\s+PLUS)\s+М[ОO]ДЕЛЬ',                         # AION LX PLUS МОДЕЛЬ
                    r'МАРКА\s+BYD\s+(YUAN\s+PLUS)\s+М[ОO]ДЕЛЬ',                        # BYD YUAN PLUS МОДЕЛЬ
                    r'МАРКА\s+AION\s+(S\s+MAX)\s+М[ОO]ДЕЛЬ',                           # AION S MAX МОДЕЛЬ
                    r'([A-Z]+)\s+(LX\s+PLUS)\s+М[ОO]ДЕЛЬ',                              # Generic LX PLUS МОДЕЛЬ
                    r'([A-Z]+)\s+(YUAN\s+PLUS)\s+М[ОO]ДЕЛЬ',                            # Generic YUAN PLUS МОДЕЛЬ
                    r'([A-Z]+)\s+(S\s+MAX)\s+М[ОO]ДЕЛЬ',                                # Generic S MAX МОДЕЛЬ
                    # Standard patterns
                    r'(?:М[ОO]ДЕЛЬ[-:]?\s*)([A-ZА-ЯЁ0-9\-]+(?:[\s-][A-ZА-ЯЁ0-9]+)*)',  # Standard МОДЕЛЬ
                    r'(?:MОДЕЛЬ[-:]?\s*)([A-ZА-ЯЁ0-9\-]+(?:[\s-][A-ZА-ЯЁ0-9]+)*)',     # Cyrillic М
                    r'(?:MODEL[-:]?\s*)([A-ZА-ЯЁ0-9\-]+(?:[\s-][A-ZА-ЯЁ0-9]+)*)',      # English MODEL
                    r'([A-Z]+)\s+([A-ZА-ЯЁ0-9\s\-]+?)(?:\s*:)',                        # Brand Model: format
                    # Complex patterns with commas and codes
                    r'М[ОO]ДЕЛЬ[-:]?([A-ZА-ЯЁ0-9]+),',                                  # МОДЕЛЬ:CODE,
                    r'([A-ZА-ЯЁ0-9\s]+)\s+М[ОO]ДЕЛЬ[-:]?([A-ZА-ЯЁ0-9]+)',             # Brand Model МОДЕЛЬ:CODE
                ]

                model_match_explicit = None
                for pattern in model_patterns_explicit:
                    match = re.search(pattern, rest)
                    if match:
                        if len(match.groups()) > 1:  # Brand Model: format or ultra-specific patterns
                            model = match.group(2).strip() if match.group(2) else match.group(1).strip()
                        else:
                            model = match.group(1).strip()
                        model_match_explicit = True
                        break

                # Special case: Extract model names that appear before МОДЕЛЬ codes
                if not model_match_explicit and 'МОДЕЛЬ:' in rest:
                    # Look for model names before МОДЕЛЬ: codes
                    pre_model_patterns = [
                        r'([A-Z]+\s+PLUS)\s+М[ОO]ДЕЛЬ:',  # MODEL PLUS МОДЕЛЬ:
                        r'([A-Z]+\s+MAX)\s+М[ОO]ДЕЛЬ:',   # MODEL MAX МОДЕЛЬ:
                        r'([A-Z]+)\s+М[ОO]ДЕЛЬ:',         # MODEL МОДЕЛЬ:
                    ]
                    for pattern in pre_model_patterns:
                        match = re.search(pattern, rest)
                        if match:
                            model = match.group(1).strip()
                            model_match_explicit = True
                            break

                if not model_match_explicit:
                    # Enhanced model extraction patterns
                    model_patterns = [
                        # Ultra-specific patterns for exact failure cases
                        r'АВТОМОБИЛИ\s+С\s+РАБОЧИМ\s+ОБ\.\s+ДВ\.\s+\d+\s+СМ3\s+МАРКИ\s+TOYOTA\s+(HIGHLANDER)', # Exact TOYOTA HIGHLANDER pattern
                        r'МАРКИ\s+TOYOTA\s+(HIGHLANDER)\s+\d{4}',                       # TOYOTA HIGHLANDER specific
                        r'МАРКА\s+AION\s+(LX\s+PLUS)\s+М[ОO]ДЕЛЬ',                     # AION LX PLUS МОДЕЛЬ
                        r'МАРКА\s+BYD\s+(YUAN\s+PLUS)\s+М[ОO]ДЕЛЬ',                    # BYD YUAN PLUS МОДЕЛЬ
                        r'МАРКА\s+AION\s+(S\s+MAX)\s+М[ОO]ДЕЛЬ',                       # AION S MAX МОДЕЛЬ
                        r'ЭЛЕКТРОМОБИЛЬ\s+XPENG\s+М[ОO]ДЕЛЬ:([A-Z0-9]+)',             # XPENG МОДЕЛЬ:CODE
                        r'ЭЛЕКТРОМОБИЛЬ\s+CHANGAN\s+Г-ВЫП',                            # CHANGAN (no model available)
                        r'МАРКИ\s+[A-Z]+\s+([A-Z]+\s+PLUS)\s+М[ОO]ДЕЛЬ',              # Brand MODEL PLUS МОДЕЛЬ
                        r'МАРКИ\s+[A-Z]+\s+([A-Z]+\s+MAX)\s+М[ОO]ДЕЛЬ',               # Brand MODEL MAX МОДЕЛЬ
                        # Pattern 1: Multi-word models with PLUS, MAX, etc.
                        r'^[\s\-]*([A-ZА-ЯЁ0-9]+(?:[\s\-]+(?:PLUS|MAX|PRO|SPORT|HYBRID|EV|TURBO|AMG|M|S|X|Y|Z))*(?:[\s\-]+[A-ZА-ЯЁ0-9]+)*)(?:\s+(?:ГОД|ГОДА|ВЫПУСКА|КУЗОВ|VIN|СМ3|ДВИГ|РАБОЧИЙ|ОБЪЁМ|С\s+РАБОЧИМ|\d{4}|Г\.ВЫП|Б/У|М[ОO]ДЕЛЬ)|\s*$)',
                        # Pattern 2: Model in middle of text (TOYOTA HIGHLANDER pattern)
                        r'МАРКИ\s+[A-Z]+\s+([A-ZА-ЯЁ0-9\s\-]+?)\s+(?:\d{4}|ГОД)',
                        # Pattern 3: Direct model after brand (handles multi-word models)
                        r'^[\s\-]*([A-ZА-ЯЁ0-9]+(?:[\s\-]+[A-ZА-ЯЁ0-9]+)*?)(?:\s+(?:ГОД|ГОДА|ВЫПУСКА|КУЗОВ|VIN|СМ3|ДВИГ|РАБОЧИЙ|ОБЪЁМ|С\s+РАБОЧИМ|\d{4}|Г\.ВЫП|Б/У)|\s*$)',
                        # Pattern 4: Model in quotes
                        r'["\']([A-ZА-ЯЁ0-9\s\-]+)["\']',
                        # Pattern 5: Single letter/number models (for EVs like Y, U, S)
                        r'^[\s\-]*([A-ZА-ЯЁ0-9]{1,3})(?:\s+(?:ГОД|ГОДА|ВЫПУСКА|Г\.ВЫП|\d{4}))',
                        # Pattern 6: Model after specific keywords
                        r'(?:МАРКИ\s+[A-ZА-ЯЁ]+\s+)([A-ZА-ЯЁ0-9]+(?:\s+[A-ZА-ЯЁ0-9]+)*?)(?:\s+(?:ГОД|ГОДА|ВЫПУСКА|\d{4}))',
                        # Pattern 7: Model with numbers and letters (RAV 4, etc.)
                        r'^[\s\-]*([A-ZА-ЯЁ]+\s+\d+)(?:\s*:|\s+(?:ГОД|ГОДА|Г\.|ВИП))',
                        # Pattern 8: Model without brand context
                        r'([A-ZА-ЯЁ0-9]+(?:\s+[A-ZА-ЯЁ0-9]+)*?)\s+Г[-\.]?ВЫП',
                    ]

                    for pattern in model_patterns:
                        model_match = re.search(pattern, rest)
                        if model_match:
                            model = model_match.group(1).strip()
                            break
                break

    # Enhanced model cleanup
    if model:
        # Remove year suffixes and common non-model suffixes
        model = re.sub(r'\s+\d{4}$', '', model)
        model = re.sub(r'\s+(?:Г\s+ВЫП|ГОДА?|ВЫП|ВИН|ЦВЕТ).*$', '', model)
        model = re.sub(r'\s+(?:МОДЕЛЬ|MODEL).*$', '', model)
        # Remove "1ШТ" from model names
        model = re.sub(r'[\s\-]*1ШТ[\s\-]*', ' ', model)
        # Remove " Г" and "Г-ВЫП" patterns from model names
        model = re.sub(r'[\s\-]*Г[\s\-]*ВЫП[\s\-]*', ' ', model)  # Remove "Г-ВЫП", "Г ВЫП", etc.
        model = re.sub(r'[\s\-]*Г[\s\-]*$', '', model)  # Remove trailing " Г", "-Г", etc.
        model = re.sub(r'[\s\-]*Г[\s\-]+', ' ', model)  # Remove " Г " in middle of text

        # Special cleanup for HYUNDAI models - remove leading "I"
        if brand and brand.upper() == 'HYUNDAI':
            model = re.sub(r'^I\s+', '', model)  # Remove "I " at the beginning
            model = re.sub(r'^I-', '', model)    # Remove "I-" at the beginning

        # Clean up extra spaces and dashes
        model = re.sub(r'\s+', ' ', model).strip()
        model = re.sub(r'^[\-\s]+|[\-\s]+$', '', model)

        # Filter out obvious non-model strings
        if (len(model) < 1 or
            model in ['I', 'А', 'М', 'A', 'M'] or  # Single letters that are likely prefixes
            re.match(r'^[A-Z0-9]{10,}$', model)):  # Long alphanumeric strings (likely codes)
            model = None

    # Extract VIN code after КУЗОВ, ШАССИ, VIN, or ВИН (handle Cyrillic chars and typos)
    vin_patterns = [
        # Pattern 1: VIN directly followed by ЦВЕТ (no separators)
        r'(?:ВИН|VIN):([A-ZА-ЯЁ0-9]{17})ЦВЕТ',
        r'(?:ШАССИ|ШАССЫ):([A-ZА-ЯЁ0-9]{17})ЦВЕТ',
        # Pattern 2: VIN without separators (followed by ЦВЕТ or other keywords)
        r'(?:VIN|ВИН)[\s:]*([A-ZА-ЯЁ0-9]{17})(?:ЦВЕТ|ДВИГ|ОБЪЁМ|$)',
        # Pattern 3: ШАССИ without separators (followed by ЦВЕТ)
        r'(?:ШАССИ|ШАССЫ|ШАСС)[\s:]*([A-ZА-ЯЁ0-9]{17})(?:ЦВЕТ|ОБЪЁМ|ДВИГ|$)',
        # Pattern 4: КУЗОВ without separators (followed by ЦВЕТ, ДВИГ, etc.)
        r'(?:КУЗОВ)[\s:]*([A-ZА-ЯЁ0-9]{17})(?:ЦВЕТ|ДВИГ|ОБЪЁМ|РАБОЧИЙ|$)',
        # Pattern 5: After КУЗОВ, ШАССИ, VIN, ВИН keywords (handle typos)
        r'(?:КУЗОВ|ШАССИ|ШАССЫ|ШАСС|VIN|ВИН|ВМН)[\s:№\(\)-]*([A-ZА-ЯЁ0-9]{8,})',
        # Pattern 6: Standalone VIN-like codes (17 characters, handle Cyrillic)
        r'\b([A-ZА-ЯЁ0-9]{17})\b',
        # Pattern 7: After specific keywords with various separators
        r'(?:КУЗОВ|ШАССИ|VIN|ВИН|ВМН)[\s:№\(\)\-]*([A-ZА-ЯЁ0-9\-]{10,})',
    ]

    body_type = None
    for pattern in vin_patterns:
        vin_match = re.search(pattern, car_detail)
        if vin_match:
            potential_vin = vin_match.group(1).strip()
            # Clean up the VIN (remove leading dashes, convert Cyrillic to Latin)
            potential_vin = re.sub(r'^[\-]+', '', potential_vin)
            # Convert common Cyrillic characters to Latin equivalents
            cyrillic_to_latin = {'А': 'A', 'В': 'B', 'С': 'C', 'Е': 'E', 'Н': 'H', 'К': 'K', 'М': 'M', 'О': 'O', 'Р': 'P', 'Т': 'T', 'У': 'Y', 'Х': 'X'}
            for cyr, lat in cyrillic_to_latin.items():
                potential_vin = potential_vin.replace(cyr, lat)
            # Only accept if it looks like a valid VIN (8+ alphanumeric characters)
            if len(potential_vin) >= 8 and re.match(r'^[A-Z0-9\-]+$', potential_vin):
                body_type = potential_vin
                break

    return year, brand, model, body_type

def analyze_failure_patterns(df):
    """Analyze failure patterns to identify improvement opportunities"""
    print("\n🔍 FAILURE PATTERN ANALYSIS:")

    # Model extraction failures
    model_failures = df[df['Модель'].isna()]
    print(f"\n📊 MODEL EXTRACTION FAILURES: {len(model_failures)} cases")

    if len(model_failures) > 0:
        print("Sample model extraction failures:")
        for i, row in model_failures.head(10).iterrows():
            text = row['маркировка'][:100] + "..." if len(row['маркировка']) > 100 else row['маркировка']
            print(f"  {i}: {row['Марка']} | {text}")

    # Brand extraction failures
    brand_failures = df[df['Марка'].isna()]
    print(f"\n📊 BRAND EXTRACTION FAILURES: {len(brand_failures)} cases")

    if len(brand_failures) > 0:
        print("Sample brand extraction failures:")
        for i, row in brand_failures.head(5).iterrows():
            text = row['маркировка'][:100] + "..." if len(row['маркировка']) > 100 else row['маркировка']
            print(f"  {i}: {text}")

    # VIN extraction failures
    vin_failures = df[df['Кузов'].isna()]
    print(f"\n📊 VIN EXTRACTION FAILURES: {len(vin_failures)} cases")

    if len(vin_failures) > 0:
        print("Sample VIN extraction failures:")
        for i, row in vin_failures.head(5).iterrows():
            text = row['маркировка'][:100] + "..." if len(row['маркировка']) > 100 else row['маркировка']
            print(f"  {i}: {text}")

def analyze_extraction_accuracy(input_file):
    """Analyze extraction accuracy for improvement suggestions"""

    try:
        df = pd.read_excel(input_file)

        print("=== DETAILED ANALYSIS ===")
        print(f"Total records: {len(df)}")

        results = []
        for i in range(len(df)):
            raw = df.iloc[i]['маркировка']
            year, brand, model, body = extract_car_details(raw)

            results.append({
                'index': i,
                'raw': raw,
                'year': year,
                'brand': brand,
                'model': model,
                'body': body
            })

            print(f"\n{i}: {raw}")
            print(f"   -> Year: {year}, Brand: {brand}, Model: {model}, Body: {body}")

        # Analysis summary
        print("\n=== EXTRACTION SUMMARY ===")
        total = len(results)
        year_extracted = sum(1 for r in results if r['year'] is not None)
        brand_extracted = sum(1 for r in results if r['brand'] is not None)
        model_extracted = sum(1 for r in results if r['model'] is not None)
        body_extracted = sum(1 for r in results if r['body'] is not None)

        print(f"Year extraction: {year_extracted}/{total} ({year_extracted/total*100:.1f}%)")
        print(f"Brand extraction: {brand_extracted}/{total} ({brand_extracted/total*100:.1f}%)")
        print(f"Model extraction: {model_extracted}/{total} ({model_extracted/total*100:.1f}%)")
        print(f"Body extraction: {body_extracted}/{total} ({body_extracted/total*100:.1f}%)")

        # Identify problematic cases
        print("\n=== PROBLEMATIC CASES ===")
        for r in results:
            issues = []
            if r['year'] is None: issues.append("No Year")
            if r['brand'] is None: issues.append("No Brand")
            if r['model'] is None: issues.append("No Model")
            if r['body'] is None: issues.append("No Body")

            if issues:
                print(f"{r['index']}: {', '.join(issues)}")
                print(f"   Raw: {r['raw'][:100]}...")

        return results

    except Exception as e:
        print(f"❌ Analysis error: {e}")
        return []

def main():
    print("🚗 Car Details Extractor")
    print("=" * 50)

    # Let user select input file
    input_file = select_excel_file()
    if input_file is None:
        return

    # Generate output filename based on input filename
    base_name = os.path.splitext(input_file)[0]
    output_file = f"clean-{base_name}.xlsx"

    print(f"📤 Output will be saved as: {output_file}")

    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', 1000)

    try:
        print(f"\n📖 Reading file: {input_file}")
        df = pd.read_excel(input_file)
        print(f"📋 Found {len(df)} records in the file")

        if 'маркировка' not in df.columns:
            print("❌ Error: Column 'маркировка' not found in the Excel file.")
            print("Available columns:", list(df.columns))
            return

        print("🔄 Processing car details extraction...")
        df[['Год', 'Марка', 'Модель', 'Кузов']] = df['маркировка'].apply(extract_car_details).apply(pd.Series)
        print("✅ Extraction completed!")

        print("Cleaned data sample:")
        print(df[['маркировка', 'Год', 'Марка', 'Модель', 'Кузов']].head(10))

        # Run summary analysis for large dataset
        print("\n" + "="*50)
        if len(df) > 100:  # For large datasets, show only summary
            print("=== LARGE DATASET SUMMARY ===")
            print(f"Total records processed: {len(df)}")
            year_extracted = df['Год'].notna().sum()
            brand_extracted = df['Марка'].notna().sum()
            model_extracted = df['Модель'].notna().sum()
            vin_extracted = df['Кузов'].notna().sum()

            print(f"Year extraction: {year_extracted}/{len(df)} ({year_extracted/len(df)*100:.1f}%)")
            print(f"Brand extraction: {brand_extracted}/{len(df)} ({brand_extracted/len(df)*100:.1f}%)")
            print(f"Model extraction: {model_extracted}/{len(df)} ({model_extracted/len(df)*100:.1f}%)")
            print(f"VIN extraction: {vin_extracted}/{len(df)} ({vin_extracted/len(df)*100:.1f}%)")

            overall_rate = ((year_extracted + brand_extracted + model_extracted + vin_extracted) / (len(df) * 4) * 100)
            print(f"\nOverall success rate: {overall_rate:.1f}%")

            # Analyze failure patterns for improvement
            analyze_failure_patterns(df)
        else:
            analyze_extraction_accuracy(input_file)

        # Save the cleaned data to an Excel file
        print(f"\n💾 Saving results to: {output_file}")
        df.to_excel(output_file, index=False)
        print(f"✅ Extraction complete! Results saved to '{output_file}'.")
        print(f"📊 Processed {len(df)} records from '{input_file}'.")

    except FileNotFoundError:
        print(f"❌ File '{input_file}' not found.")
    except Exception as e:
        print(f"❌ An error occurred: {e}")



if __name__ == '__main__':
    main()